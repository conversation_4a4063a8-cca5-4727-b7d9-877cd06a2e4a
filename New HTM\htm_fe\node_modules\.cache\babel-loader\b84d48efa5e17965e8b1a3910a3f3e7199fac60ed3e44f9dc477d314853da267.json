{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\ui\\\\PlayerAnswerInput.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef } from \"react\";\nimport { usePlayer } from \"../../context/playerContext\";\nimport { useTimeStart } from \"../../context/timeListenerContext\";\nimport { useAppDispatch, useAppSelector } from \"../../app/store\";\nimport { setPlayerAnswer } from \"../../app/store/slices/gameSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlayerAnswerInput = ({\n  isHost,\n  question,\n  isDisabled,\n  playerAnswerRef: externalPlayerAnswerRef\n}) => {\n  _s();\n  const inputRef = useRef(null);\n  const {\n    timeElapsed,\n    setPlayerAnswerTime\n  } = useTimeStart();\n  const dispatch = useAppDispatch();\n  const currentPlayer = useAppSelector(state => state.room.currentPlayer);\n  const playerAnswer = useAppSelector(state => {\n    var _state$room$currentPl;\n    return (_state$room$currentPl = state.room.currentPlayer) === null || _state$room$currentPl === void 0 ? void 0 : _state$room$currentPl.answer;\n  });\n  const {\n    playerAnswerRef: contextPlayerAnswerRef\n  } = usePlayer();\n  const handleKeyDown = event => {\n    if (event.key === \"Enter\") {\n      const inputElement = event.target;\n      playerAnswerRef.current = inputElement.value;\n      dispatch(setPlayerAnswer({\n        uid: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.uid) || \"\",\n        answer: inputElement.value,\n        time: timeElapsed\n      }));\n      inputElement.value = \"\";\n    }\n  };\n  useEffect(() => {\n    console.log(\"playerAnswerRef.current when question\", playerAnswerRef.current);\n    // Clear input when question changes\n    if (inputRef.current) {\n      inputRef.current.value = \"\";\n    }\n    playerAnswerRef.current = \"\";\n    dispatch(setPlayerAnswer({\n      uid: (currentPlayer === null || currentPlayer === void 0 ? void 0 : currentPlayer.uid) || \"\",\n      answer: \"\",\n      time: 0\n    }));\n    console.log(\"playerAnswerRef.current after clear\", playerAnswerRef.current);\n  }, [question]);\n  if (isHost) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      ref: inputRef,\n      className: \"w-full h-14 border border-gray-300 rounded-lg px-4 text-lg text-center\",\n      placeholder: \"Type your answer...\",\n      onKeyDown: handleKeyDown,\n      disabled: isDisabled\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-2 text-lg text-white\",\n      children: playerAnswer ? `Your answer: ${playerAnswer}` : \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 9\n  }, this);\n};\n_s(PlayerAnswerInput, \"VYdG09Oxy78LSxOh3K38A8EguRw=\", false, function () {\n  return [useTimeStart, useAppDispatch, useAppSelector, useAppSelector, usePlayer];\n});\n_c = PlayerAnswerInput;\nexport default PlayerAnswerInput;\nvar _c;\n$RefreshReg$(_c, \"PlayerAnswerInput\");", "map": {"version": 3, "names": ["useEffect", "useRef", "usePlayer", "useTimeStart", "useAppDispatch", "useAppSelector", "setPlayerAnswer", "jsxDEV", "_jsxDEV", "PlayerAnswerInput", "isHost", "question", "isDisabled", "playerAnswerRef", "externalPlayerAnswerRef", "_s", "inputRef", "timeElapsed", "setPlayerAnswerTime", "dispatch", "currentPlayer", "state", "room", "<PERSON><PERSON><PERSON><PERSON>", "_state$room$currentPl", "answer", "contextPlayerAnswerRef", "handleKeyDown", "event", "key", "inputElement", "target", "current", "value", "uid", "time", "console", "log", "className", "children", "type", "ref", "placeholder", "onKeyDown", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/ui/PlayerAnswerInput.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport { Question } from \"../../shared/types\";\r\nimport { usePlayer } from \"../../context/playerContext\";\r\nimport { useTimeStart } from \"../../context/timeListenerContext\";\r\nimport { useAppDispatch, useAppSelector } from \"../../app/store\"\r\nimport { setPlayerAnswer } from \"../../app/store/slices/gameSlice\";\r\n\r\ninterface PlayerAnswerInputProps {\r\n    isHost: boolean;\r\n    question: Question | undefined;\r\n    isDisabled?: boolean;\r\n    playerAnswerRef?: React.RefObject<string>;\r\n}\r\n\r\nconst PlayerAnswerInput: React.FC<PlayerAnswerInputProps> = ({ isHost, question, isDisabled, playerAnswerRef: externalPlayerAnswerRef }) => {\r\n    const inputRef = useRef<HTMLInputElement>(null);\r\n    const { timeElapsed, setPlayerAnswerTime } = useTimeStart()\r\n    const dispatch = useAppDispatch()\r\n    const currentPlayer = useAppSelector((state) => state.room.currentPlayer)\r\n    const playerAnswer = useAppSelector((state) => state.room.currentPlayer?.answer)\r\n    const { playerAnswerRef: contextPlayerAnswerRef } = usePlayer()\r\n\r\n    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (event.key === \"Enter\") {\r\n            const inputElement = event.target as HTMLInputElement;\r\n            playerAnswerRef.current = inputElement.value;\r\n\r\n            dispatch(setPlayerAnswer({\r\n                uid: currentPlayer?.uid || \"\",\r\n                answer: inputElement.value,\r\n                time: timeElapsed\r\n            }))\r\n\r\n            inputElement.value = \"\";\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        console.log(\"playerAnswerRef.current when question\", playerAnswerRef.current);\r\n        // Clear input when question changes\r\n        if (inputRef.current) {\r\n            inputRef.current.value = \"\";\r\n        }\r\n        playerAnswerRef.current = \"\";\r\n        dispatch(setPlayerAnswer({\r\n            uid: currentPlayer?.uid || \"\",\r\n            answer: \"\",\r\n            time: 0\r\n        }))\r\n\r\n        console.log(\"playerAnswerRef.current after clear\", playerAnswerRef.current);\r\n\r\n    }, [question]);\r\n\r\n    if (isHost) return null;\r\n\r\n    return (\r\n        <div className=\"mt-2 w-full\">\r\n            <input\r\n                type=\"text\"\r\n                ref={inputRef}\r\n                className=\"w-full h-14 border border-gray-300 rounded-lg px-4 text-lg text-center\"\r\n                placeholder=\"Type your answer...\"\r\n                onKeyDown={handleKeyDown}\r\n                disabled={isDisabled}\r\n            />\r\n            <p className=\"mt-2 text-lg text-white\">\r\n                {playerAnswer ? `Your answer: ${playerAnswer}` : \"\"}\r\n            </p>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default PlayerAnswerInput\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAkB,OAAO;AAEnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,SAASC,eAAe,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASnE,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,UAAU;EAAEC,eAAe,EAAEC;AAAwB,CAAC,KAAK;EAAAC,EAAA;EACxI,MAAMC,QAAQ,GAAGf,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM;IAAEgB,WAAW;IAAEC;EAAoB,CAAC,GAAGf,YAAY,CAAC,CAAC;EAC3D,MAAMgB,QAAQ,GAAGf,cAAc,CAAC,CAAC;EACjC,MAAMgB,aAAa,GAAGf,cAAc,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACF,aAAa,CAAC;EACzE,MAAMG,YAAY,GAAGlB,cAAc,CAAEgB,KAAK;IAAA,IAAAG,qBAAA;IAAA,QAAAA,qBAAA,GAAKH,KAAK,CAACC,IAAI,CAACF,aAAa,cAAAI,qBAAA,uBAAxBA,qBAAA,CAA0BC,MAAM;EAAA,EAAC;EAChF,MAAM;IAAEZ,eAAe,EAAEa;EAAuB,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAE/D,MAAMyB,aAAa,GAAIC,KAA4C,IAAK;IACpE,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACvB,MAAMC,YAAY,GAAGF,KAAK,CAACG,MAA0B;MACrDlB,eAAe,CAACmB,OAAO,GAAGF,YAAY,CAACG,KAAK;MAE5Cd,QAAQ,CAACb,eAAe,CAAC;QACrB4B,GAAG,EAAE,CAAAd,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEc,GAAG,KAAI,EAAE;QAC7BT,MAAM,EAAEK,YAAY,CAACG,KAAK;QAC1BE,IAAI,EAAElB;MACV,CAAC,CAAC,CAAC;MAEHa,YAAY,CAACG,KAAK,GAAG,EAAE;IAC3B;EACJ,CAAC;EAEDjC,SAAS,CAAC,MAAM;IACZoC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAExB,eAAe,CAACmB,OAAO,CAAC;IAC7E;IACA,IAAIhB,QAAQ,CAACgB,OAAO,EAAE;MAClBhB,QAAQ,CAACgB,OAAO,CAACC,KAAK,GAAG,EAAE;IAC/B;IACApB,eAAe,CAACmB,OAAO,GAAG,EAAE;IAC5Bb,QAAQ,CAACb,eAAe,CAAC;MACrB4B,GAAG,EAAE,CAAAd,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEc,GAAG,KAAI,EAAE;MAC7BT,MAAM,EAAE,EAAE;MACVU,IAAI,EAAE;IACV,CAAC,CAAC,CAAC;IAEHC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAExB,eAAe,CAACmB,OAAO,CAAC;EAE/E,CAAC,EAAE,CAACrB,QAAQ,CAAC,CAAC;EAEd,IAAID,MAAM,EAAE,OAAO,IAAI;EAEvB,oBACIF,OAAA;IAAK8B,SAAS,EAAC,aAAa;IAAAC,QAAA,gBACxB/B,OAAA;MACIgC,IAAI,EAAC,MAAM;MACXC,GAAG,EAAEzB,QAAS;MACdsB,SAAS,EAAC,wEAAwE;MAClFI,WAAW,EAAC,qBAAqB;MACjCC,SAAS,EAAEhB,aAAc;MACzBiB,QAAQ,EAAEhC;IAAW;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eACFxC,OAAA;MAAG8B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EACjChB,YAAY,GAAG,gBAAgBA,YAAY,EAAE,GAAG;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd,CAAC;AAACjC,EAAA,CAzDIN,iBAAmD;EAAA,QAERN,YAAY,EACxCC,cAAc,EACTC,cAAc,EACfA,cAAc,EACiBH,SAAS;AAAA;AAAA+C,EAAA,GAN3DxC,iBAAmD;AA2DzD,eAAeA,iBAAiB;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}