{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\ui\\\\PlayerAnswerInput.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport { usePlayer } from \"../../context/playerContext\";\nimport { useTimeStart } from \"../../context/timeListenerContext\";\nimport { useAppDispatch } from \"../../app/store\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlayerAnswerInput = ({\n  isHost,\n  question,\n  isDisabled,\n  playerAnswerRef: externalPlayerAnswerRef\n}) => {\n  _s();\n  const inputRef = useRef(null);\n  const {\n    timeElapsed,\n    setPlayerAnswerTime\n  } = useTimeStart();\n  const [playerAnswer, setPlayerAnswer] = useState(\"\");\n  const dispatch = useAppDispatch();\n  const {\n    playerAnswerRef: contextPlayerAnswerRef\n  } = usePlayer();\n\n  // Use external ref if provided, otherwise use context ref\n  const playerAnswerRef = externalPlayerAnswerRef || contextPlayerAnswerRef;\n  const handleKeyDown = event => {\n    if (event.key === \"Enter\") {\n      const inputElement = event.target;\n      playerAnswerRef.current = inputElement.value;\n      setPlayerAnswerTime(timeElapsed);\n      setPlayerAnswer(playerAnswerRef.current);\n      inputElement.value = \"\";\n    }\n  };\n  useEffect(() => {\n    console.log(\"playerAnswerRef.current when question\", playerAnswerRef.current);\n\n    // Clear input when question changes\n    if (inputRef.current) {\n      inputRef.current.value = \"\";\n    }\n    playerAnswerRef.current = \"\";\n    setPlayerAnswer(\"\");\n    console.log(\"playerAnswerRef.current after clear\", playerAnswerRef.current);\n  }, [question]);\n  if (isHost) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      ref: inputRef,\n      className: \"w-full h-14 border border-gray-300 rounded-lg px-4 text-lg text-center\",\n      placeholder: \"Type your answer...\",\n      onKeyDown: handleKeyDown,\n      disabled: isDisabled\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-2 text-lg text-white\",\n      children: playerAnswer ? `Your answer: ${playerAnswer}` : \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 9\n  }, this);\n};\n_s(PlayerAnswerInput, \"Wk2mF622AibHIPw3iMkdoH6v+9Q=\", false, function () {\n  return [useTimeStart, useAppDispatch, usePlayer];\n});\n_c = PlayerAnswerInput;\nexport default PlayerAnswerInput;\nvar _c;\n$RefreshReg$(_c, \"PlayerAnswerInput\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "usePlayer", "useTimeStart", "useAppDispatch", "jsxDEV", "_jsxDEV", "PlayerAnswerInput", "isHost", "question", "isDisabled", "playerAnswerRef", "externalPlayerAnswerRef", "_s", "inputRef", "timeElapsed", "setPlayerAnswerTime", "<PERSON><PERSON><PERSON><PERSON>", "setPlayerAnswer", "dispatch", "contextPlayerAnswerRef", "handleKeyDown", "event", "key", "inputElement", "target", "current", "value", "console", "log", "className", "children", "type", "ref", "placeholder", "onKeyDown", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/ui/PlayerAnswerInput.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport { Question } from \"../../shared/types\";\r\nimport { usePlayer } from \"../../context/playerContext\";\r\nimport { useTimeStart } from \"../../context/timeListenerContext\";\r\nimport { useAppDispatch } from \"../../app/store\"\r\n\r\n\r\ninterface PlayerAnswerInputProps {\r\n    isHost: boolean;\r\n    question: Question | undefined;\r\n    isDisabled?: boolean;\r\n    playerAnswerRef?: React.RefObject<string>;\r\n}\r\n\r\nconst PlayerAnswerInput: React.FC<PlayerAnswerInputProps> = ({ isHost, question, isDisabled, playerAnswerRef: externalPlayerAnswerRef }) => {\r\n    const inputRef = useRef<HTMLInputElement>(null);\r\n    const {timeElapsed, setPlayerAnswerTime} = useTimeStart()\r\n    const [playerAnswer, setPlayerAnswer] = useState(\"\");\r\n    const dispatch = useAppDispatch()\r\n    \r\n    const {playerAnswerRef: contextPlayerAnswerRef} = usePlayer()\r\n\r\n    // Use external ref if provided, otherwise use context ref\r\n    const playerAnswerRef = externalPlayerAnswerRef || contextPlayerAnswerRef;\r\n\r\n    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (event.key === \"Enter\") {\r\n            const inputElement = event.target as HTMLInputElement;\r\n            playerAnswerRef.current = inputElement.value;\r\n            setPlayerAnswerTime(timeElapsed)\r\n            setPlayerAnswer(playerAnswerRef.current)\r\n            inputElement.value = \"\";\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        console.log(\"playerAnswerRef.current when question\", playerAnswerRef.current);\r\n        \r\n        // Clear input when question changes\r\n        if (inputRef.current) {\r\n            inputRef.current.value = \"\";\r\n        }\r\n        playerAnswerRef.current = \"\";\r\n        setPlayerAnswer(\"\")\r\n\r\n        console.log(\"playerAnswerRef.current after clear\", playerAnswerRef.current);\r\n        \r\n    }, [question]);\r\n\r\n    if (isHost) return null;\r\n\r\n    return (\r\n        <div className=\"mt-2 w-full\">\r\n            <input\r\n                type=\"text\"\r\n                ref={inputRef}\r\n                className=\"w-full h-14 border border-gray-300 rounded-lg px-4 text-lg text-center\"\r\n                placeholder=\"Type your answer...\"\r\n                onKeyDown={handleKeyDown}\r\n                disabled={isDisabled}\r\n            />\r\n            <p className=\"mt-2 text-lg text-white\">\r\n                {playerAnswer? `Your answer: ${playerAnswer}`: \"\"}\r\n            </p>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default PlayerAnswerInput\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAEnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,cAAc,QAAQ,iBAAiB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUhD,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,UAAU;EAAEC,eAAe,EAAEC;AAAwB,CAAC,KAAK;EAAAC,EAAA;EACxI,MAAMC,QAAQ,GAAGd,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAM;IAACe,WAAW;IAAEC;EAAmB,CAAC,GAAGb,YAAY,CAAC,CAAC;EACzD,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAMkB,QAAQ,GAAGf,cAAc,CAAC,CAAC;EAEjC,MAAM;IAACO,eAAe,EAAES;EAAsB,CAAC,GAAGlB,SAAS,CAAC,CAAC;;EAE7D;EACA,MAAMS,eAAe,GAAGC,uBAAuB,IAAIQ,sBAAsB;EAEzE,MAAMC,aAAa,GAAIC,KAA4C,IAAK;IACpE,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACvB,MAAMC,YAAY,GAAGF,KAAK,CAACG,MAA0B;MACrDd,eAAe,CAACe,OAAO,GAAGF,YAAY,CAACG,KAAK;MAC5CX,mBAAmB,CAACD,WAAW,CAAC;MAChCG,eAAe,CAACP,eAAe,CAACe,OAAO,CAAC;MACxCF,YAAY,CAACG,KAAK,GAAG,EAAE;IAC3B;EACJ,CAAC;EAED5B,SAAS,CAAC,MAAM;IACZ6B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAElB,eAAe,CAACe,OAAO,CAAC;;IAE7E;IACA,IAAIZ,QAAQ,CAACY,OAAO,EAAE;MAClBZ,QAAQ,CAACY,OAAO,CAACC,KAAK,GAAG,EAAE;IAC/B;IACAhB,eAAe,CAACe,OAAO,GAAG,EAAE;IAC5BR,eAAe,CAAC,EAAE,CAAC;IAEnBU,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAElB,eAAe,CAACe,OAAO,CAAC;EAE/E,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;EAEd,IAAID,MAAM,EAAE,OAAO,IAAI;EAEvB,oBACIF,OAAA;IAAKwB,SAAS,EAAC,aAAa;IAAAC,QAAA,gBACxBzB,OAAA;MACI0B,IAAI,EAAC,MAAM;MACXC,GAAG,EAAEnB,QAAS;MACdgB,SAAS,EAAC,wEAAwE;MAClFI,WAAW,EAAC,qBAAqB;MACjCC,SAAS,EAAEd,aAAc;MACzBe,QAAQ,EAAE1B;IAAW;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eACFlC,OAAA;MAAGwB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EACjCd,YAAY,GAAE,gBAAgBA,YAAY,EAAE,GAAE;IAAE;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd,CAAC;AAAC3B,EAAA,CApDIN,iBAAmD;EAAA,QAEVJ,YAAY,EAEtCC,cAAc,EAEmBF,SAAS;AAAA;AAAAuC,EAAA,GANzDlC,iBAAmD;AAsDzD,eAAeA,iBAAiB;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}