import { useEffect, useRef, useState } from "react";
import { Question } from "../../shared/types";
import { usePlayer } from "../../context/playerContext";
import { useTimeStart } from "../../context/timeListenerContext";
import { useAppDispatch, useAppSelector } from "../../app/store"
import { setPlayerAnswer } from "../../app/store/slices/gameSlice";

interface PlayerAnswerInputProps {
    isHost: boolean;
    question: Question | undefined;
    isDisabled?: boolean;
    playerAnswerRef?: React.RefObject<string>;
}

const PlayerAnswerInput: React.FC<PlayerAnswerInputProps> = ({ isHost, question, isDisabled, playerAnswerRef: externalPlayerAnswerRef }) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const { timeElapsed, setPlayerAnswerTime } = useTimeStart()
    const dispatch = useAppDispatch()
    const currentPlayer = useAppSelector((state) => state.room.currentPlayer)
    const playerAnswer = useAppSelector((state) => state.room.currentPlayer?.answer)
    const { playerAnswerRef: contextPlayerAnswerRef } = usePlayer()

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === "Enter") {
            const inputElement = event.target as HTMLInputElement;
            // playerAnswerRef.current = inputElement.value;

            dispatch(setPlayerAnswer({
                uid: currentPlayer?.uid || "",
                answer: inputElement.value,
                time: timeElapsed
            }))

            inputElement.value = "";
        }
    };

    useEffect(() => {
        // Clear input when question changes
        if (inputRef.current) {
            inputRef.current.value = "";
        }
        // playerAnswerRef.current = "";
        dispatch(setPlayerAnswer({
            uid: currentPlayer?.uid || "",
            answer: "",
            time: 0
        }))

    }, [question]);

    if (isHost) return null;

    return (
        <div className="mt-2 w-full">
            <input
                type="text"
                ref={inputRef}
                className="w-full h-14 border border-gray-300 rounded-lg px-4 text-lg text-center"
                placeholder="Type your answer..."
                onKeyDown={handleKeyDown}
                disabled={isDisabled}
            />
            <p className="mt-2 text-lg text-white">
                {playerAnswer ? `Your answer: ${playerAnswer}` : ""}
            </p>
        </div>
    );
};

export default PlayerAnswerInput
